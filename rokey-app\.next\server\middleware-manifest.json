{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|public\\/).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|public/).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "ruZT8WVjL2lt8Sy_2_R6R", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "QIO2FxewUlXf/7AHIT2B/6cD212Lh2bC+3YhJISHrok=", "__NEXT_PREVIEW_MODE_ID": "5508c0486e130c5f31010ddaa6e01b75", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "5e914edd03baacec3ce81174060c48daff4dd83af939f684c7e3c5e7acd28773", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "0ac6893646b3b7492b6e9dc6797d0c8421418d9921d0871c9ad83ed74ea182ec"}}}, "functions": {"/api/external/v1/chat/completions/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/chat/completions/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/app/api/external/v1/chat/completions/route.js"], "name": "app/api/external/v1/chat/completions/route", "page": "/api/external/v1/chat/completions/route", "matchers": [{"regexp": "^/api/external/v1/chat/completions$", "originalSource": "/api/external/v1/chat/completions"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "ruZT8WVjL2lt8Sy_2_R6R", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "QIO2FxewUlXf/7AHIT2B/6cD212Lh2bC+3YhJISHrok=", "__NEXT_PREVIEW_MODE_ID": "5508c0486e130c5f31010ddaa6e01b75", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "5e914edd03baacec3ce81174060c48daff4dd83af939f684c7e3c5e7acd28773", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "0ac6893646b3b7492b6e9dc6797d0c8421418d9921d0871c9ad83ed74ea182ec"}}}, "sortedMiddleware": ["/"]}