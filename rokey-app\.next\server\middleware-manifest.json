{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|public\\/).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|public/).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "zuRmXbdOpGIrdcz3ydUW6", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "QIO2FxewUlXf/7AHIT2B/6cD212Lh2bC+3YhJISHrok=", "__NEXT_PREVIEW_MODE_ID": "d77ee0537cec0eb1935d68afdfc7910b", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "f153d1e43d60fb5fba8254e725b780d4c55f7755aec70c5ad5c303ee6d523a88", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "46f4e8cce9cef113124e9fa91d023feb7b2e1b20aef991d1f36a78eac2002239"}}}, "functions": {"/api/external/v1/chat/completions/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/chat/completions/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/app/api/external/v1/chat/completions/route.js"], "name": "app/api/external/v1/chat/completions/route", "page": "/api/external/v1/chat/completions/route", "matchers": [{"regexp": "^/api/external/v1/chat/completions$", "originalSource": "/api/external/v1/chat/completions"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "zuRmXbdOpGIrdcz3ydUW6", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "QIO2FxewUlXf/7AHIT2B/6cD212Lh2bC+3YhJISHrok=", "__NEXT_PREVIEW_MODE_ID": "d77ee0537cec0eb1935d68afdfc7910b", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "f153d1e43d60fb5fba8254e725b780d4c55f7755aec70c5ad5c303ee6d523a88", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "46f4e8cce9cef113124e9fa91d023feb7b2e1b20aef991d1f36a78eac2002239"}}}, "sortedMiddleware": ["/"]}