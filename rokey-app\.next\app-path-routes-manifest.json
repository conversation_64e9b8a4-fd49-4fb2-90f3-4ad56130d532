{"/api/activity/route": "/api/activity", "/api/admin/cleanup-semantic-cache/route": "/api/admin/cleanup-semantic-cache", "/api/admin/populate-cost-tiers/route": "/api/admin/populate-cost-tiers", "/api/auth/check-pending-payment/route": "/api/auth/check-pending-payment", "/api/auth/free-signup/route": "/api/auth/free-signup", "/api/analytics/summary/route": "/api/analytics/summary", "/api/chat/conversations/route": "/api/chat/conversations", "/api/cache/invalidate/route": "/api/cache/invalidate", "/api/chat/messages/route": "/api/chat/messages", "/api/chat/messages/delete-after-timestamp/route": "/api/chat/messages/delete-after-timestamp", "/api/chat/messages/update-by-timestamp/route": "/api/chat/messages/update-by-timestamp", "/api/cleanup/pending-users/route": "/api/cleanup/pending-users", "/api/custom-configs/[configId]/default-chat-key/route": "/api/custom-configs/[configId]/default-chat-key", "/api/custom-configs/[configId]/default-key-handler/[apiKeyId]/route": "/api/custom-configs/[configId]/default-key-handler/[apiKeyId]", "/api/custom-configs/[configId]/keys/[apiKeyId]/complexity-assignments/route": "/api/custom-configs/[configId]/keys/[apiKeyId]/complexity-assignments", "/api/custom-configs/[configId]/routing/route": "/api/custom-configs/[configId]/routing", "/api/custom-configs/route": "/api/custom-configs", "/api/custom-configs/[configId]/route": "/api/custom-configs/[configId]", "/api/debug/checkout/route": "/api/debug/checkout", "/api/debug/auth-test/route": "/api/debug/auth-test", "/api/debug/supabase-test/route": "/api/debug/supabase-test", "/api/debug/clear-cache/route": "/api/debug/clear-cache", "/api/documents/[documentId]/route": "/api/documents/[documentId]", "/api/documents/list/route": "/api/documents/list", "/api/documents/upload/route": "/api/documents/upload", "/api/external/v1/async/result/[jobId]/route": "/api/external/v1/async/result/[jobId]", "/api/documents/search/route": "/api/documents/search", "/api/external/v1/async/status/[jobId]/route": "/api/external/v1/async/status/[jobId]", "/api/external/v1/async/submit/route": "/api/external/v1/async/submit", "/api/internal/async/process/route": "/api/internal/async/process", "/api/keys/[apiKeyId]/roles/[roleName]/route": "/api/keys/[apiKeyId]/roles/[roleName]", "/api/keys/[apiKeyId]/roles/route": "/api/keys/[apiKeyId]/roles", "/api/internal/classify-multi-role/route": "/api/internal/classify-multi-role", "/api/keys/route": "/api/keys", "/api/keys/[apiKeyId]/route": "/api/keys/[apiKeyId]", "/api/logs/route": "/api/logs", "/api/orchestration/classify-roles/route": "/api/orchestration/classify-roles", "/api/orchestration/process-step/route": "/api/orchestration/process-step", "/api/orchestration/start/route": "/api/orchestration/start", "/api/orchestration/status/[executionId]/route": "/api/orchestration/status/[executionId]", "/api/orchestration/stream/[executionId]/route": "/api/orchestration/stream/[executionId]", "/api/orchestration/synthesis-fallback/[executionId]/route": "/api/orchestration/synthesis-fallback/[executionId]", "/api/orchestration/synthesis-stream-direct/[executionId]/route": "/api/orchestration/synthesis-stream-direct/[executionId]", "/api/orchestration/synthesis-stream/[executionId]/route": "/api/orchestration/synthesis-stream/[executionId]", "/api/playground/route": "/api/playground", "/api/pricing/tiers/route": "/api/pricing/tiers", "/api/providers/list-models/route": "/api/providers/list-models", "/api/quality-analytics/route": "/api/quality-analytics", "/api/stripe/create-checkout-session/route": "/api/stripe/create-checkout-session", "/api/stripe/customer-portal/route": "/api/stripe/customer-portal", "/api/stripe/subscription-status/route": "/api/stripe/subscription-status", "/api/quality-feedback/route": "/api/quality-feedback", "/api/stripe/webhooks/route": "/api/stripe/webhooks", "/api/test-subscription/route": "/api/test-subscription", "/api/test/semantic-cache/route": "/api/test/semantic-cache", "/api/system-status/route": "/api/system-status", "/api/training/jobs/route": "/api/training/jobs", "/api/user-api-keys/[keyId]/route": "/api/user-api-keys/[keyId]", "/api/training/jobs/upsert/route": "/api/training/jobs/upsert", "/api/user-api-keys/route": "/api/user-api-keys", "/api/user/custom-roles/route": "/api/user/custom-roles", "/api/user/subscription-tier/route": "/api/user/subscription-tier", "/api/user/delete-account/route": "/api/user/delete-account", "/api/v1/chat/completions/route": "/api/v1/chat/completions", "/api/user/custom-roles/[customRoleId]/route": "/api/user/custom-roles/[customRoleId]", "/auth/callback/route": "/auth/callback", "/favicon.ico/route": "/favicon.ico", "/_not-found/page": "/_not-found", "/add-keys/page": "/add-keys", "/analytics/page": "/analytics", "/auth/recover/page": "/auth/recover", "/auth/reset-password/page": "/auth/reset-password", "/auth/signin/page": "/auth/signin", "/auth/signup/page": "/auth/signup", "/billing/page": "/billing", "/auth/verify-email/page": "/auth/verify-email", "/checkout/page": "/checkout", "/debug-session/page": "/debug-session", "/logs/page": "/logs", "/my-models/[configId]/page": "/my-models/[configId]", "/my-models/page": "/my-models", "/page": "/", "/dashboard/page": "/dashboard", "/pricing/page": "/pricing", "/playground/page": "/playground", "/routing-setup/[configId]/page": "/routing-setup/[configId]", "/routing-setup/page": "/routing-setup", "/success/page": "/success", "/training/page": "/training", "/(app)/settings/page": "/settings", "/contact/page": "/contact", "/blog/ai-api-gateway-2025-guide/page": "/blog/ai-api-gateway-2025-guide", "/blog/ai-model-selection-guide/page": "/blog/ai-model-selection-guide", "/blog/bootstrap-lean-startup-2025/page": "/blog/bootstrap-lean-startup-2025", "/blog/build-ai-powered-saas/page": "/blog/build-ai-powered-saas", "/blog/cost-effective-ai-development/page": "/blog/cost-effective-ai-development", "/blog/page": "/blog", "/blog/roukey-ai-routing-strategies/page": "/blog/roukey-ai-routing-strategies", "/cookies/page": "/cookies", "/privacy/page": "/privacy", "/about/page": "/about", "/features/page": "/features", "/terms/page": "/terms", "/routing-strategies/page": "/routing-strategies", "/docs/page": "/docs", "/security/page": "/security", "/api/external/v1/chat/completions/route": "/api/external/v1/chat/completions"}