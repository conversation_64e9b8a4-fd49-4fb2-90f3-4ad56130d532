(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4680],{21884:(e,s,t)=>{"use strict";t.d(s,{C1:()=>a.A,KS:()=>i.A,Pi:()=>r.A,fK:()=>n.A,qh:()=>l.A});var a=t(6865),r=t(55628),i=t(67695),l=t(52589),n=t(74500)},96141:(e,s,t)=>{Promise.resolve().then(t.bind(t,97009))},97009:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>g});var a=t(95155),r=t(12115),i=t(6874),l=t.n(i),n=t(66766),o=t(55020),c=t(18593),d=t(10184),m=t(48987),u=t(74500),x=t(21394),b=t(52643),h=t(35695),p=t(64198);function f(){let[e,s]=(0,r.useState)(""),[t,i]=(0,r.useState)(""),[f,g]=(0,r.useState)(!1),[y,j]=(0,r.useState)(!1),[v,N]=(0,r.useState)(""),[w,k]=(0,r.useState)(!1),[S,_]=(0,r.useState)(""),[C,P]=(0,r.useState)(!1),[A,E]=(0,r.useState)(""),I=(0,h.useRouter)(),R=(0,h.useSearchParams)(),D=(0,b.createSupabaseBrowserClient)(),{success:q,error:z}=(0,p.dj)();(0,r.useEffect)(()=>{},[I,R,D.auth]);let F=async s=>{s.preventDefault(),j(!0),N("");try{let{data:s,error:a}=await D.auth.signInWithPassword({email:e,password:t});if(a)throw a;if(s.user){await new Promise(e=>setTimeout(e,500));let e=R.get("redirectTo"),t=R.get("plan"),a=R.get("email"),r=R.get("checkout_user_id");try{let{data:t}=await D.from("user_profiles").select("subscription_tier, subscription_status").eq("id",s.user.id).single();if(t&&"active"===t.subscription_status)return void(e?I.push(e):I.push("/dashboard"));let a=s.user.user_metadata,r=null==a?void 0:a.payment_status,i=null==a?void 0:a.plan;if("pending"===r&&i&&["starter","professional","enterprise"].includes(i))return void I.push("/pricing")}catch(e){}if(r&&t&&["starter","professional","enterprise"].includes(t)){let e="/checkout?plan=".concat(t,"&user_id=").concat(s.user.id).concat(a?"&email=".concat(encodeURIComponent(a)):"");I.push(e)}else if(t&&["starter","professional","enterprise"].includes(t))try{let{data:e}=await D.from("user_profiles").select("subscription_tier, subscription_status").eq("id",s.user.id).single();if(e&&"free"===e.subscription_tier&&"active"===e.subscription_status)I.push("/dashboard");else{let e="/checkout?plan=".concat(t,"&user_id=").concat(s.user.id).concat(a?"&email=".concat(encodeURIComponent(a)):"");I.push(e)}}catch(e){I.push("/dashboard")}else e?I.push(e):I.push("/dashboard")}}catch(e){N(e.message||"Invalid email or password. Please try again.")}finally{j(!1)}},K=async e=>{if(e.preventDefault(),P(!0),E(""),!S.trim()){E("Please enter your email address"),P(!1);return}try{let{error:e}=await D.auth.resetPasswordForEmail(S,{redirectTo:"".concat(window.location.origin,"/auth/reset-password")});if(e)throw e;q("Password reset email sent!","Check your inbox for instructions to reset your password."),k(!1),_("")}catch(e){E(e.message||"Failed to send reset email. Please try again."),z("Failed to send reset email",e.message||"Please try again.")}finally{P(!1)}};return(0,a.jsxs)("div",{className:"min-h-screen bg-white relative overflow-hidden flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:[(0,a.jsx)(x.A,{gridSize:50,opacity:.064,color:"#000000",variant:"subtle",animated:!0,className:"fixed inset-0"}),(0,a.jsx)("div",{className:"absolute inset-0",children:(0,a.jsx)("div",{className:"absolute inset-0",style:{backgroundImage:"radial-gradient(circle, rgba(255, 107, 53, 0.12) 1px, transparent 1px)",backgroundSize:"100px 100px",mask:"radial-gradient(ellipse 70% 70% at center, black 30%, transparent 70%)",WebkitMask:"radial-gradient(ellipse 70% 70% at center, black 30%, transparent 70%)"}})}),(0,a.jsx)("div",{className:"relative z-10 w-full max-w-6xl mx-auto",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center",children:[(0,a.jsx)(o.PY1.div,{initial:{opacity:0,x:-50},animate:{opacity:1,x:0},transition:{duration:.8},className:"hidden lg:block",children:(0,a.jsxs)("div",{className:"bg-gradient-to-br from-[#ff6b35] to-[#f7931e] rounded-3xl p-12 text-white relative overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute inset-0 opacity-20",style:{backgroundImage:"\n                    linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),\n                    linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px)\n                  ",backgroundSize:"30px 30px"}}),(0,a.jsxs)("div",{className:"relative z-10",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center mb-6 backdrop-blur-sm",children:(0,a.jsx)(n.default,{src:"/roukey_logo.png",alt:"RouKey",width:48,height:48,className:"w-12 h-12 object-contain",priority:!0})}),(0,a.jsx)("h1",{className:"text-4xl font-bold mb-4",children:"Welcome to RouKey"}),(0,a.jsxs)("p",{className:"text-xl text-white/90 mb-8",children:["Access to ",(0,a.jsx)("span",{className:"font-bold",children:"UNLIMITED"})," AI requests across 300+ models"]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-white rounded-full"}),(0,a.jsx)("span",{className:"text-white/90",children:"Intelligent Role Routing"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-white rounded-full"}),(0,a.jsx)("span",{className:"text-white/90",children:"Enterprise Security"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-white rounded-full"}),(0,a.jsx)("span",{className:"text-white/90",children:"No Request Limits"})]})]})]})]})}),(0,a.jsxs)(o.PY1.div,{initial:{opacity:0,x:50},animate:{opacity:1,x:0},transition:{duration:.8,delay:.2},className:"w-full max-w-md mx-auto lg:mx-0",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsxs)(l(),{href:"/",className:"inline-flex items-center space-x-3 mb-8 justify-center lg:justify-start",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-white rounded-xl flex items-center justify-center shadow-lg border border-gray-100 p-1",children:(0,a.jsx)(n.default,{src:"/roukey_logo.png",alt:"RouKey",width:40,height:40,className:"w-full h-full object-contain",priority:!0})}),(0,a.jsx)("span",{className:"text-3xl font-bold text-black",children:"RouKey"})]}),(0,a.jsx)("h2",{className:"text-4xl font-bold text-black mb-3",children:"Sign In"}),(0,a.jsx)("p",{className:"text-gray-600 text-lg",children:"Welcome back to your AI gateway"})]}),(0,a.jsxs)("div",{className:"bg-white rounded-3xl shadow-xl border border-gray-100 p-8 backdrop-blur-sm relative overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute inset-0 opacity-5",style:{backgroundImage:"\n                    linear-gradient(rgba(255, 107, 53, 0.1) 1px, transparent 1px),\n                    linear-gradient(90deg, rgba(255, 107, 53, 0.1) 1px, transparent 1px)\n                  ",backgroundSize:"20px 20px"}}),(0,a.jsxs)("div",{className:"relative z-10",children:["account_created"===R.get("message")&&(0,a.jsx)("div",{className:"mb-6 p-4 bg-green-50 border border-green-200 rounded-xl",children:(0,a.jsx)("p",{className:"text-green-600 text-sm font-medium",children:"✅ Account created successfully! Please sign in to complete your checkout."})}),"complete_payment"===R.get("message")&&(0,a.jsx)("div",{className:"mb-6 p-4 bg-amber-50 border border-amber-200 rounded-xl",children:(0,a.jsxs)("p",{className:"text-amber-700 text-sm font-medium",children:["⚠️ You have a pending payment for your ",R.get("plan")||"premium"," plan. Please sign in to complete your checkout."]})}),v&&(0,a.jsx)("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 rounded-xl",children:(0,a.jsx)("p",{className:"text-red-600 text-sm",children:v})}),(0,a.jsxs)("form",{onSubmit:F,className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-sm font-semibold text-gray-800 mb-3",children:"\uD83D\uDCE7 Email Address"}),(0,a.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:e,onChange:e=>s(e.target.value),className:"w-full px-5 py-4 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-[#ff6b35] focus:border-[#ff6b35] transition-all duration-300 text-gray-900 placeholder-gray-500 bg-gray-50 focus:bg-white",placeholder:"Enter your email"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"password",className:"block text-sm font-semibold text-gray-800 mb-3",children:"\uD83D\uDD12 Password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{id:"password",name:"password",type:f?"text":"password",autoComplete:"current-password",required:!0,value:t,onChange:e=>i(e.target.value),className:"w-full px-5 py-4 pr-14 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-[#ff6b35] focus:border-[#ff6b35] transition-all duration-300 text-gray-900 placeholder-gray-500 bg-gray-50 focus:bg-white",placeholder:"Enter your password"}),(0,a.jsx)("button",{type:"button",onClick:()=>g(!f),className:"absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-[#ff6b35] transition-colors",children:f?(0,a.jsx)(m.A,{className:"h-5 w-5"}):(0,a.jsx)(d.A,{className:"h-5 w-5"})})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{id:"remember-me",name:"remember-me",type:"checkbox",className:"h-5 w-5 text-[#ff6b35] focus:ring-[#ff6b35] border-gray-300 rounded-lg"}),(0,a.jsx)("label",{htmlFor:"remember-me",className:"ml-3 block text-sm font-medium text-gray-700",children:"Remember me"})]}),(0,a.jsx)("button",{type:"button",onClick:()=>{_(e),E(""),k(!0)},className:"text-sm text-[#ff6b35] hover:text-[#e55a2b] font-semibold transition-colors",children:"Forgot password?"})]}),(0,a.jsx)("button",{type:"submit",disabled:y,className:"w-full bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white py-4 px-6 rounded-xl font-bold text-lg hover:from-[#e55a2b] hover:to-[#e8851a] focus:ring-4 focus:ring-[#ff6b35]/30 focus:ring-offset-2 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transform hover:scale-[1.02]",children:y?(0,a.jsxs)("div",{className:"flex items-center justify-center",children:[(0,a.jsx)("div",{className:"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"}),"Signing in..."]}):"Sign In"})]})]})]}),(0,a.jsxs)("div",{className:"text-center mt-8 space-y-3",children:[(0,a.jsxs)("p",{className:"text-gray-600 text-lg",children:["Don't have an account?"," ",(0,a.jsx)(l(),{href:"/auth/signup",className:"text-[#ff6b35] hover:text-[#e55a2b] font-bold transition-colors",children:"Sign up for free"})]}),(0,a.jsxs)("p",{className:"text-gray-500 text-sm",children:["Can't remember your account status?"," ",(0,a.jsx)(l(),{href:"/auth/recover",className:"text-[#ff6b35] hover:text-[#e55a2b] font-medium transition-colors",children:"Check account recovery"})]})]})]})]})}),w&&(0,a.jsx)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:(0,a.jsxs)("div",{className:"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 transition-opacity",onClick:()=>k(!1)}),(0,a.jsx)("div",{className:"inline-block align-bottom bg-white rounded-2xl text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full",children:(0,a.jsxs)("div",{className:"bg-white px-6 pt-6 pb-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-xl font-bold text-gray-900",children:"Reset Your Password"}),(0,a.jsx)("button",{onClick:()=>k(!1),className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,a.jsx)(u.A,{className:"h-6 w-6"})})]}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"Enter your email address and we'll send you a link to reset your password."}),A&&(0,a.jsx)("div",{className:"mb-4 p-3 bg-red-50 border border-red-200 rounded-lg",children:(0,a.jsx)("p",{className:"text-red-600 text-sm",children:A})}),(0,a.jsxs)("form",{onSubmit:K,children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("label",{htmlFor:"resetEmail",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{id:"resetEmail",type:"email",value:S,onChange:e=>_(e.target.value),placeholder:"Enter your email address",className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent transition-all duration-200",required:!0}),(0,a.jsx)(c.A,{className:"h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2"})]})]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsx)("button",{type:"button",onClick:()=>k(!1),className:"flex-1 px-4 py-3 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-xl hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors",children:"Cancel"}),(0,a.jsx)("button",{type:"submit",disabled:C,className:"flex-1 px-4 py-3 text-sm font-medium text-white bg-[#ff6b35] border border-transparent rounded-xl hover:bg-[#e55a2b] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#ff6b35] disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:C?(0,a.jsxs)("div",{className:"flex items-center justify-center",children:[(0,a.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"}),"Sending..."]}):"Send Reset Link"})]})]})]})})]})})]})}function g(){return(0,a.jsx)(r.Suspense,{fallback:(0,a.jsx)("div",{className:"min-h-screen bg-white flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-8 h-8 border-2 border-[#ff6b35] border-t-transparent rounded-full animate-spin mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Loading..."})]})}),children:(0,a.jsx)(f,{})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[7871,2115,8888,1459,5738,9968,6060,6308,4755,563,2662,8669,4703,622,2432,408,6642,7706,7544,2138,4518,9248,2324,7358],()=>s(96141)),_N_E=e.O()}]);